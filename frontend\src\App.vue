<script setup>
// 不需要导入任何组件，因为我们使用路由来管理页面
</script>

<template>
  <div class="app">
    <header class="app-header">
      <nav class="navbar">
        <div class="logo-container">
          <img src="@/assets/vue.svg" class="logo" alt="Vue logo" />
          <h1>低代码平台</h1>
        </div>
        <div class="nav-links">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/editor" class="nav-link">编辑器</router-link>
          <router-link to="/forms" class="nav-link">表单管理</router-link>
          <router-link to="/about" class="nav-link">关于我们</router-link>
        </div>
      </nav>
    </header>

    <main class="app-main">
      <!-- 路由视图 - 这里将显示匹配的路由组件 -->
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
  </div>
</template>

<style lang="less">
// 变量定义
@primary-color: #41b883;
@secondary-color: #35495e;
@dark-color: #2c3e50;
@success-color: #2ecc71;
@white: #fff;
@text-color: #333;
@header-height: 60px;

// 全局样式重置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: @text-color;
  line-height: 1.6;
}

.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;

  // 顶部导航栏
  &-header {
    flex-shrink: 0;
    height: @header-height;
    background-color: @secondary-color;
    border-bottom: 1px solid @dark-color;
    z-index: 1000;
  }

  // 主内容区域
  &-main {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  color: @white;
}

.logo-container {
  display: flex;
  align-items: center;

  h1 {
    font-size: 18px;
    font-weight: 600;
  }
}

.logo {
  height: 32px;
  margin-right: 12px;
}

.nav-links {
  display: flex;
  gap: 0;
}

.nav-link {
  color: @white;
  text-decoration: none;
  font-weight: 500;
  padding: 18px 20px;
  height: @header-height;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
  border-bottom: 3px solid transparent;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &.router-link-active {
    background-color: @primary-color;
    border-bottom-color: @success-color;
  }
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .navbar {
    padding: 0 15px;
  }

  .logo-container {
    h1 {
      font-size: 16px;
    }
  }

  .nav-links {
    gap: 0;
  }

  .nav-link {
    padding: 18px 15px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .logo-container {
    h1 {
      display: none;
    }
  }

  .nav-link {
    padding: 18px 10px;
    font-size: 12px;
  }
}
</style>
