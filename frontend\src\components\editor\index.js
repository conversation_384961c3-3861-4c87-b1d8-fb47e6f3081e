import ElButton from '@/components/editor/ElButton.vue';
import ElInput from '@/components/editor/ElInput.vue';
import ElText from '@/components/editor/ElText.vue';

// 组件列表
export const components = {
  ElButton,
  ElInput,
  ElText
};

// 注册所有组件
export function registerEditorComponents(app) {
  Object.entries(components).forEach(([name, component]) => {
    app.component(name, component);
  });
}

export default {
  install(app) {
    registerEditorComponents(app);
  }
};
