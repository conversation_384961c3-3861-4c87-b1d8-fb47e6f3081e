<template>
  <div class="111-container">
    <div class="form-wrapper">
      <h1 class="form-title">
        111
      </h1>

      <form
        @submit.prevent="handleSubmit"
        class="form-content"
      >
        <div
          :style="{ position: 'absolute', left: '210px', top: '60px', width: '120px', minHeight: '40px' }"
        >
          <input
            v-model="formData.field1748174750182.2205"
            type="text"
            placeholder="请输入内容"
            class="form-input"
          />
        </div>
        <div
          :style="{ position: 'absolute', left: '360px', top: '60px', width: '120px', minHeight: '40px' }"
        >
          <div class="form-text">
            文本内容
          </div>
        </div>
        <div
          :style="{ position: 'absolute', left: '210px', top: '120px', width: '120px', minHeight: '40px' }"
        >
          <input
            v-model="formData.field1748174857883.1968"
            type="text"
            placeholder="请输入内容"
            class="form-input"
          />
        </div>

        <div class="form-actions">
          <button
            type="submit"
            class="submit-btn"
          >
            提交
          </button>
          <button
            type="button"
            class="reset-btn"
            @click="resetForm"
          >
            重置
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'

// 表单数据
const formData = reactive({
  field1748174750182.2205: '',
  field1748174857883.1968: ''
})

// 提交表单
const handleSubmit = () => {
  console.log('表单数据:', formData)
  // 在这里添加你的提交逻辑
  alert('表单提交成功！')
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
}
</script>

<style scoped>
.form-wrapper {
  max-width: 800px;
  margin: 0 auto;
  padding: 30px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-title {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
}

.form-description {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.5;
}

.form-content {
  position: relative;
  min-height: 400px;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  background-color: #fafafa;
  margin-bottom: 30px;
  padding: 20px;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #41b883;
  box-shadow: 0 0 0 2px rgba(65, 184, 131, 0.2);
}

.form-button {
  padding: 10px 20px;
  background-color: #41b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.form-button:hover {
  background-color: #35495e;
}

.form-text {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  padding: 8px 0;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.submit-btn {
  padding: 12px 30px;
  background-color: #67c23a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.submit-btn:hover {
  background-color: #5daf34;
}

.reset-btn {
  padding: 12px 30px;
  background-color: white;
  color: #333;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.reset-btn:hover {
  background-color: #fafbfc;
}
</style>