// 测试代码生成器
import { generateVueComponent } from './codeGenerator.js';

// 模拟表单数据
const testForm = {
  name: '测试表单',
  description: '这是一个测试表单'
};

// 模拟表单元素
const testElements = [
  {
    id: 1,
    type: 'ElInput',
    props: {
      placeholder: '请输入姓名'
    },
    position: { x: 100, y: 50 },
    size: { width: 200, height: 40 }
  },
  {
    id: 2,
    type: 'ElInput',
    props: {
      placeholder: '' // 空的placeholder测试
    },
    position: { x: 100, y: 100 },
    size: { width: 200, height: 40 }
  },
  {
    id: 3,
    type: 'ElButton',
    props: {
      text: '提交按钮'
    },
    position: { x: 100, y: 150 },
    size: { width: 100, height: 40 }
  }
];

// 生成代码
const generatedCode = generateVueComponent(testForm, testElements);
console.log('生成的代码：');
console.log(generatedCode);
