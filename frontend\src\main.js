import { createApp } from 'vue'
import '@/style.css'
import '@/assets/icons.css'
import App from '@/App.vue'
import router from '@/router'
import EditorComponents from '@/components/editor'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const app = createApp(App)

// 使用Element Plus
app.use(ElementPlus)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

// 使用路由
app.use(router)

// 注册编辑器组件
app.use(EditorComponents)

// 挂载应用
app.mount('#app')
