import { createRouter, createWebHistory } from 'vue-router'

// 导入页面组件
import Home from '@/views/Home.vue'
import About from '@/views/About.vue'
import NotFound from '@/views/NotFound.vue'
import Editor from '@/views/Editor.vue'
import Forms from '@/views/Forms.vue'
import Preview from '@/views/Preview.vue'

// 定义路由
const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/editor',
    name: 'Editor',
    component: Editor,
    meta: {
      title: '低代码编辑器'
    }
  },
  {
    path: '/forms',
    name: 'Forms',
    component: Forms,
    meta: {
      title: '表单管理'
    }
  },
  {
    path: '/preview',
    name: 'Preview',
    component: Preview,
    meta: {
      title: '表单预览'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      title: '关于我们'
    }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || 'Vue 3 应用'
  next()
})

export default router
